# 通用

## 基本准测

- Always reply in Simplified Chinese.
- 在所有的规则文件中，包括但不限于当前这个规则文件，所有的带有删除线的内容，全部忽略，不用参考。类似这种：~~这是一个规则。~~
- 完成任务后，不需要对任务内容进行任何的总结和陈述
- 当任务复杂可能导致执行被终止时，应将任务分成小块逐步执行，避免出现terminated等问题
- 记住，不要幻想！不要撒谎！不要刻意讨好用户！用户希望你可以诚实的看待所有任务和问题
- 当你在分析用户的需求和任务、设计功能、解决bug、修复问题等情况时，应该尽可能仔细思考，全面思考，不要漏掉任何可能导致错误的细节，全面的解决用户的任务和问题。比如：当你在解决bug时，应该尽可能全面的考虑引起bug的所有可能性，并逐一排查，而不是在检查代码后，发现了一个可能导致问题的原因，就不再管其他可能导致问题的原因了

## 项目目录结构

如果需要查看项目结构，请参考下方的项目结构内容。

注意：此结构仅供参考。具体以项目现有结构为准，因为目录可能不会及时更新。

有需要时，也可以在此基础上，创建新的文件夹。

当需要创建的新文件时，请将文件放到合适的文件夹下，以保持项目结构的合理和清晰。

**项目结构：**

├── app/                           # 应用主模块
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/              # Kotlin 代码目录
│   │   │   │   └── com/shuyi/discipline/
│   │   │   │       ├── data/      # 数据层
│   │   │   │       │   ├── model/ # 数据模型
│   │   │   │       │   ├── repository/ # 数据仓库
│   │   │   │       │   └── source/ # 数据源
│   │   │   │       ├── di/        # 依赖注入
│   │   │   │       ├── domain/    # 领域层（业务逻辑）
│   │   │   │       │   └── usecase/ # 用例
│   │   │   │       ├── ui/        # 界面层
│   │   │   │       │   ├── activity/ # Activity
│   │   │   │       │   └── viewmodel/ # ViewModel
│   │   │   │       └── util/      # 工具类
│   │   │   ├── res/               # 资源文件
│   │   │   └── AndroidManifest.xml
│   │   ├── test/                  # 单元测试
│   │   └── androidTest/           # UI测试
│   ├── build.gradle               # 模块级构建脚本
│   └── proguard-rules.pro         # 混淆规则
├── build.gradle                   # 项目级构建脚本
├── gradle/
├── gradle.properties
├── gradlew
├── gradlew.bat
└── settings.gradle





# 编程语言规则

## Kotlin通用规则

### 基本规则

- 使用合适的键来尽量减少代码重组。
- 始终声明每个变量和函数的类型（参数和返回值）。
- 避免使用 any。
- 编写详细的代码注释，并在代码中添加必要的错误处理和日志记录。

### 技术规则

- 使用Kotlin语言和Jetpack Compose进行Android应用开发。
- 遵循MVVM架构模式进行应用开发。
- 使用 Kotlin 协程、Kotlin Flow和Coroutines进行响应式编程和异步操作管理。
- 使用 Hilt 实现依赖注入。
- 使用 ViewModel 和 UI 状态，遵循单向数据流。
- 使用 Compose 导航进行屏幕管理。
- 使用 LazyColumn 和 LazyRow 进行合适的延迟加载。
- 使用Room数据库进行本地数据存储和管理。
- 使用KSP进行注解处理。
- 使用 Timber 进行日志记录。
- 使用 WorkManager 进行后台任务管理。
- 使用 Coil 进行图片加载。
- 使用 Accompanist 进行权限管理。
- 使用 Jetpack Security 进行数据加密。
- 使用 Jetpack DataStore 进行数据持久化。
- 使用 Jetpack Navigation 进行页面导航。
- 使用 Jetpack Lifecycle 进行生命周期管理。
- 在构建用户界面时，应遵循 Material3 -> Foundation -> UI 的依赖和使用优先级。首先尝试用 Material3 解决问题；如果不能满足，则考虑 Foundation；仅在极少数需要深度定制或直接操作底层API的情况下，才直接使用 UI 库的功能。
- 使用 Jetpack Compose Animation 进行动画效果开发。

### 最佳实践

- 严格遵循 Material Design 3的编码规范和最佳实践。
- 严格遵循Jetpack Compose编码规范和最佳实践。
- 严格遵循Kotlin编码规范和最佳实践。
- 严格遵循Android编码规范和最佳实践。
- 严格遵循软件工程和项目开发的最佳实践。

### 性能和质量规则

- 使用合适的状态管理来避免不必要的更新。
- 实现适当的应用生命周期管理，确保应用在前台和后台都能正常运行。
- 实现合适的内存管理，避免内存泄漏。
- 使用合适的后台处理。
- 使用Kotlin的类型系统进行严格的类型检查，提高代码质量。
- 使用合适的异常捕获程序来捕获异常，注意添加合适的日志打印

### UI规则

- 实现适配不同Android设备的自适应布局。
- 使用 MaterialTheme 进行适当的主题设置。
- 每次创建新的界面时，应当创建一套浅色主题界面，一套深色主题界面。另外要注意：应该考虑到浅色主题和深色主题下的字体颜色应该不同。要保证字体颜色合适，在不同主题下都能够清晰可见。比如在浅色模式下，由于背景多为白色，字体颜色不能偏白色，在深色模式下，背景多为黑色，字体颜色不能偏黑色
- 每次修改界面或者删除界面时，也应该同步修改或删除浅色主题和深色主题对应的界面。
- 在项目中，有设置字体大小的功能，每当新增界面或者修改界面时，应当考虑字体大小要跟随设置字体大小功能一同更新。
- 正确使用 remember 和 derivedStateOf。
- 实现适当的重组优化。
- 使用适当的 Compose 修饰符排序。
- 遵循可组合函数命名约定。
- 实现适当的预览注解。
- 实现适当的错误处理和加载状态。
- 遵循无障碍指南。
- 实现适当的动画模式。

### 命名规则

- 类使用帕斯卡命名法 (PascalCase)。
- 变量、函数和方法使用驼峰命名法 (camelCase)。
- 环境变量使用大写字母 。

# 工作流

## 工作流基本规则

- 在你完成编码任务后，除了需要调用一下“自动构建项目、修复问题的工作流”，查看下是否能够构建成功以外，不需要做以下的操作，除非用户要求你这么做：
  - 写任何的测试代码
  - 检查测试覆盖率
  - 验证修复后的代码是否正确实施
  - 检查是否有其他潜在问题需要修复
  - 最终验证修复效果和代码质量



## 自动构建项目、修复问题的工作流

每次完成单次任务的全部的编码任务后（注意是全部任务，微软），如果本次任务中对代码进行过修改，包括增加、删除、更新，则按照以下步骤，自动进行项目构建以及问题修复。

如果有需要，该工作流可以在一次对话中，启用多次。比如：当你构建了一次项目后，自己测试发现了错误，修改完代码后，需要重新构建项目，则可以再次启用此工作流。

具体工作流：

1. 调用jetbrains的execute_action_by_id工具，传递参数："Run"
2. 调用jetbrains的get_progress_indicators工具，查看返回的数据
3. 如果返回的数据中，包含"Gradle Build Running"，则说明项目还在构建中，如果不包含，则说明项目构建任务已经完成
4. 如果返回的结果表示项目还在构建中，则调用jetbrains的wait工具，传递参数“500”，也就是等待0.5秒。
5. 等待结束后，再次调用jetbrains的get_progress_indicators工具，查看返回的数据，判断项目是否还在构建中
6. 接着执行第4步，直到返回的数据表示项目已经构建完成
7. 调用jetbrains的get_project_problems工具，根据返回信息，判断一下项目中是否有报错
8. 如果有报错，就根据报错信息，修复问题
9. 修复完问题后，不要立马调用get_project_problems_jetbrains工具，来查看错误是否被修复，而是从步骤1开始执行，因为需要重新构建项目，才能判断问题是否被修复
10. 重复执行以上步骤，直到项目中没有报错信息
11. 调用desktop_commander运行命令：'powershell -Command "(New-Object Media.SoundPlayer 'E:\音频\提示铃声\常用\rest-break-started.wav').PlaySync()"'
12. 结束本次工作流。在执行完这个工作流后，不需要你使用命令来构建、安装、启动app，也不需要你来查看logcat日志。如果需要启动app或者查看日志，请告诉我，我来操作并把结果告诉你。





# 工具（MCP或者内部工具）总体规则

## 总体规则

- 对于你不清楚的技术以及复杂的需求，应该积极使用Exa Search mcp和Context7 mcp，来获取可靠的相关信息，然后再进行编码
- 优先使用自带的文本搜索工具和编辑工具，而不是mcp提供的搜索工具和编辑工具，比如jetbrains的搜索工具和编辑工具

## 禁止在工具中执行的操作清单

### 禁止在任何终端中执行的命令清单（包括你自带的Terminal工具以及mcp工具中的终端）

#### adb install -r app-name

除非是我提及，否则禁止在任何终端中，使用这种命令来安装app。如何实在需要安装app，请参考“自动构建项目、修复问题的工作流”，使用这个工作流来进行安装app。

#### ./gradlew assembleDebug

永远不要在任何终端工具中进行项目构建或编译操作。如果需要验证和测试项目能否顺利编译或者构建，请按照"自动构建项目、修复问题的工作流"标题下的步骤，进行项目构建





## 禁止自动调用的MCP工具清单

以下列出的MCP工具清单，除非是我提到让你进行调用，否则永远不要自己调用。我将会按照工具所属的MCP Server来进行分类。

### Jetbrains

- get_open_in_editor_file_path
- execute_terminai_command
- search_in_files_content



# 各工具（MCP工具或者内部工具）单独的规则

## Context7

### 自动调用触发条件

当用户提出编码需求时，如果涉及以下技术，你必须自动调用 Context7（一个查询最新文档或示例的MCP）：

#### 1. 实现新功能时

当用户提出以下类型需求时，自动调用 Context7：





