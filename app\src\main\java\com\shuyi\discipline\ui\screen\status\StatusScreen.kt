package com.shuyi.discipline.ui.screen.status

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.TextButton
import androidx.compose.runtime.*
import kotlinx.coroutines.launch
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.zIndex
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.shuyi.discipline.data.model.MonitorStatistics
import com.shuyi.discipline.data.model.MonitorType
import com.shuyi.discipline.data.model.SystemMonitorRecord
import com.shuyi.discipline.data.repository.RuntimeRecord
import com.shuyi.discipline.data.repository.RecordType
import com.shuyi.discipline.ui.components.BottomNavBar
import com.shuyi.discipline.ui.components.PageTitleBar
import com.shuyi.discipline.ui.model.UiState
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 系统状态页面
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun StatusScreen(
    onNavigateToHome: () -> Unit,
    onNavigateToScreenshots: () -> Unit,
    onNavigateToCollages: () -> Unit,
    onNavigateToConfig: () -> Unit,
    viewModel: StatusViewModel = hiltViewModel()
) {
    // 底部导航栏选中索引
    var selectedTabIndex by remember { mutableIntStateOf(3) } // 状态选项卡

    // 收集ViewModel状态
    val status24Hours by viewModel.status24Hours.collectAsStateWithLifecycle()
    val status48Hours by viewModel.status48Hours.collectAsStateWithLifecycle()
    val isRefreshing by viewModel.isRefreshing.collectAsStateWithLifecycle()
    val message by viewModel.message.collectAsStateWithLifecycle()

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        bottomBar = {
            BottomNavBar(
                selectedIndex = selectedTabIndex,
                onItemSelected = { index ->
                    selectedTabIndex = index
                    // 根据选中的标签执行相应的导航
                    when(index) {
                        0 -> onNavigateToHome()
                        1 -> onNavigateToScreenshots()
                        2 -> onNavigateToCollages()
                        3 -> { /* 已在状态页面，无需导航 */ }
                        4 -> onNavigateToConfig()
                    }
                },
                onNavigateToHome = onNavigateToHome,
                onNavigateToScreenshots = onNavigateToScreenshots,
                onNavigateToCollages = onNavigateToCollages,
                onNavigateToStatus = { /* 已在状态页面，无需导航 */ },
                onNavigateToConfig = onNavigateToConfig
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(MaterialTheme.colorScheme.background)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(horizontal = 16.dp)
            ) {
            // 标题栏
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "系统状态",
                    style = MaterialTheme.typography.headlineMedium,
                    modifier = Modifier
                        .weight(1f)
                        .combinedClickable(
                            onLongClick = {
                                // 长按插入测试数据（用于演示）
                                viewModel.insertTestData()
                            },
                            onClick = { /* 普通点击无操作 */ }
                        )
                )

                // 清空数据按钮
                IconButton(
                    onClick = { viewModel.clearAllMonitorData() },
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.errorContainer)
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "清空数据",
                        tint = MaterialTheme.colorScheme.onErrorContainer
                    )
                }

                Spacer(modifier = Modifier.width(8.dp))

                // 立即检查按钮
                IconButton(
                    onClick = { viewModel.executeImmediateMonitorCheck() },
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.primaryContainer)
                ) {
                    Icon(
                        imageVector = Icons.Default.PlayArrow,
                        contentDescription = "立即检查",
                        tint = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }

                Spacer(modifier = Modifier.width(8.dp))

                // 刷新按钮
                IconButton(
                    onClick = { viewModel.refresh() },
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.surfaceVariant)
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "刷新",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }



            // 24小时运行状态板块
            val status24HoursState = status24Hours
            when (status24HoursState) {
                is UiState.Loading -> {
                    StatusPeriodCardLoading("24小时运行状态")
                }
                is UiState.Success -> {
                    StatusPeriodCard(
                        title = "24小时运行状态",
                        statusData = status24HoursState.data
                    )
                }
                is UiState.Error -> {
                    StatusPeriodCardError("24小时运行状态", status24HoursState.message)
                }
                else -> {
                    StatusPeriodCardLoading("24小时运行状态")
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 48小时运行状态板块
            val status48HoursState = status48Hours
            when (status48HoursState) {
                is UiState.Loading -> {
                    StatusPeriodCardLoading("48小时运行状态")
                }
                is UiState.Success -> {
                    StatusPeriodCard(
                        title = "48小时运行状态",
                        statusData = status48HoursState.data
                    )
                }
                is UiState.Error -> {
                    StatusPeriodCardError("48小时运行状态", status48HoursState.message)
                }
                else -> {
                    StatusPeriodCardLoading("48小时运行状态")
                }
            }

                // 底部空间，确保内容不被底部导航栏遮挡
                Spacer(modifier = Modifier.height(16.dp))
            }

            // 弹窗型通知
            message?.let { msg ->
                ToastNotification(
                    message = msg,
                    onDismiss = { viewModel.clearMessage() },
                    modifier = Modifier
                        .align(Alignment.TopCenter)
                        .padding(top = 16.dp)
                        .zIndex(1f)
                )
            }
        }
    }
}

/**
 * 时间范围按钮
 */
@Composable
fun TimeRangeButton(
    text: String,
    isSelected: Boolean
) {
    Button(
        onClick = { /* 切换时间范围 */ },
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isSelected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.surfaceVariant,
            contentColor = if (isSelected) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurfaceVariant
        ),
        shape = RoundedCornerShape(50),
        modifier = Modifier.height(36.dp)
    ) {
        Text(
            text = text,
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 状态卡片
 */
@Composable
fun StatusCard(
    title: String,
    statusText: String,
    statusColor: Color,
    runningTime: String,
    progressPercent: Int,
    notRunningTime: String,
    timelineItems: List<TimelineItem>,
    showCurrentStatus: Boolean = true
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 卡片标题
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // 当前状态（可选显示）
            if (showCurrentStatus) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.weight(1f)
                    ) {
                        Box(
                            modifier = Modifier
                                .size(12.dp)
                                .background(statusColor, CircleShape)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(text = "当前状态")
                    }
                    Text(
                        text = statusText,
                        color = statusColor,
                        fontWeight = FontWeight.Medium
                    )
                }
            }

            // 运行时间
            Column(
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "运行时间",
                        fontSize = 14.sp,
                        modifier = Modifier.weight(1f)
                    )
                    Text(
                        text = runningTime,
                        fontSize = 14.sp
                    )
                }

                // 进度条
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(8.dp)
                        .background(MaterialTheme.colorScheme.outline, RoundedCornerShape(4.dp))
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(progressPercent / 100f)
                            .height(8.dp)
                            .background(MaterialTheme.colorScheme.primary, RoundedCornerShape(4.dp))
                    )
                }
            }

            // 未运行时间提示
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.errorContainer, RoundedCornerShape(8.dp))
                    .padding(12.dp)
            ) {
                Column {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(bottom = 4.dp)
                    ) {
                        Box(
                            modifier = Modifier
                                .size(16.dp)
                                .background(color = MaterialTheme.colorScheme.error, shape = CircleShape)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "${title.replace("状态", "")}未运行时间",
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                    Text(
                        text = notRunningTime,
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }

            // 时间线
            Column(
                modifier = Modifier.padding(top = 16.dp)
            ) {
                timelineItems.forEachIndexed { index, item ->
                    TimelineItemView(
                        item = item,
                        isLast = index == timelineItems.size - 1
                    )
                }
            }
        }
    }
}

/**
 * 时间线项数据类
 */
data class TimelineItem(
    val title: String,
    val timeRange: String,
    val description: String,
    val dotColor: Color
)

/**
 * 时间线项视图
 */
@Composable
fun TimelineItemView(
    item: TimelineItem,
    isLast: Boolean
) {
    Row(
        modifier = Modifier.padding(bottom = if (isLast) 0.dp else 16.dp)
    ) {
        // 时间线和点
        Box(
            modifier = Modifier.width(24.dp),
            contentAlignment = Alignment.TopCenter
        ) {
            if (!isLast) {
                Box(
                    modifier = Modifier
                        .width(2.dp)
                        .height(80.dp)
                        .background(MaterialTheme.colorScheme.outline)
                )
            }
            Box(
                modifier = Modifier
                    .size(10.dp)
                    .background(item.dotColor, CircleShape)
            )
        }

        // 内容
        Column(
            modifier = Modifier.padding(start = 8.dp)
        ) {
            Text(
                text = item.title,
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp
            )
            Text(
                text = item.timeRange,
                color = MaterialTheme.colorScheme.onSecondaryContainer,
                fontSize = 14.sp
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = item.description,
                color = MaterialTheme.colorScheme.onSurface,
                fontSize = 14.sp
            )
        }
    }
}

/**
 * 状态卡片加载状态
 */
@Composable
fun StatusCardLoading(title: String) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Text(
                text = "加载中...",
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 状态卡片错误状态
 */
@Composable
fun StatusCardError(title: String, errorMessage: String) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Text(
                text = "加载失败: $errorMessage",
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.error
            )
        }
    }
}

/**
 * 格式化运行时间
 */
private fun formatRunningTime(normalTime: Long, totalTime: Long): String {
    val normalHours = normalTime / (1000 * 60 * 60)
    val normalMinutes = (normalTime % (1000 * 60 * 60)) / (1000 * 60)
    val totalHours = totalTime / (1000 * 60 * 60)

    return "${normalHours}小时${normalMinutes}分钟 / ${totalHours}小时"
}

/**
 * 格式化持续时间
 */
private fun formatDuration(duration: Long): String {
    val hours = duration / (1000 * 60 * 60)
    val minutes = (duration % (1000 * 60 * 60)) / (1000 * 60)
    val seconds = (duration % (1000 * 60)) / 1000

    return when {
        hours > 0 -> "${hours}小时${minutes}分${seconds}秒"
        minutes > 0 -> "${minutes}分${seconds}秒"
        seconds > 0 -> "${seconds}秒"
        else -> "0秒"
    }
}

/**
 * 转换为时间线项目
 */
private fun convertToTimelineItems(records: List<SystemMonitorRecord>): List<TimelineItem> {
    val dateFormat = SimpleDateFormat("MM-dd HH:mm", Locale.getDefault())

    return records.take(5).map { record ->
        val startTime = dateFormat.format(Date(record.startTime))
        val endTime = if (record.endTime != null) {
            dateFormat.format(Date(record.endTime))
        } else {
            "进行中"
        }

        val timeRange = if (record.endTime != null) {
            "$startTime - $endTime (${formatDuration(record.duration)})"
        } else {
            "$startTime - $endTime"
        }

        TimelineItem(
            title = when {
                record.description.contains("后台运行") -> "后台服务停止"
                record.description.contains("截屏功能") -> "截屏功能停止"
                else -> "系统异常"
            },
            timeRange = timeRange,
            description = record.description,
            dotColor = if (record.isDeviceShutdown) {
                Color(0xFFFF9800) // Orange for device shutdown
            } else {
                Color(0xFFF44336) // Red for other errors
            }
        )
    }
}

/**
 * 状态时间段卡片
 */
@Composable
fun StatusPeriodCard(
    title: String,
    statusData: SystemStatusData
) {
    var showDetailDialog by remember { mutableStateOf<DetailDialogType?>(null) }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .animateContentSize(), // 添加内容大小变化动画
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 卡片标题
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // 三个状态项
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 未运行总时长
                StatusItem(
                    title = "未运行总时长",
                    duration = statusData.totalDowntime,
                    color = MaterialTheme.colorScheme.error,
                    onClick = { showDetailDialog = DetailDialogType.TOTAL }
                )

                // App未运行时长（新监控系统）
                StatusItem(
                    title = "App未运行时长",
                    duration = statusData.newAppDowntime,
                    color = Color(0xFF4CAF50), // 使用明亮的绿色
                    onClick = { showDetailDialog = DetailDialogType.NEW_APP }
                )

                // 截图服务未运行时长
                StatusItem(
                    title = "截图服务未运行时长",
                    duration = statusData.screenshotServiceDowntime,
                    color = MaterialTheme.colorScheme.secondary,
                    onClick = { showDetailDialog = DetailDialogType.SCREENSHOT_SERVICE }
                )
            }
        }
    }

    // 详情弹窗
    showDetailDialog?.let { dialogType ->
        when (dialogType) {
            DetailDialogType.TOTAL -> {
                TotalDetailDialog(
                    onDismiss = { showDetailDialog = null }
                )
            }
            DetailDialogType.NEW_APP -> {
                NewAppDetailDialog(
                    onDismiss = { showDetailDialog = null }
                )
            }
            else -> {
                DetailDialog(
                    title = when (dialogType) {
                        DetailDialogType.APP -> "App未运行时长详情"
                        DetailDialogType.SCREENSHOT_SERVICE -> "截图服务未运行时长详情"
                        else -> ""
                    },
                    records = when (dialogType) {
                        DetailDialogType.APP -> statusData.appDowntimeRecords
                        DetailDialogType.SCREENSHOT_SERVICE -> statusData.screenshotServiceDowntimeRecords
                        else -> emptyList()
                    },
                    onDismiss = { showDetailDialog = null }
                )
            }
        }
    }
}

/**
 * 状态项
 */
@Composable
fun StatusItem(
    title: String,
    duration: Long,
    color: Color,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .background(
                color = color.copy(alpha = 0.1f),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 状态指示点
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(color, CircleShape)
        )

        Spacer(modifier = Modifier.width(12.dp))

        // 标题
        Text(
            text = title,
            style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
            modifier = Modifier.weight(1f)
        )

        // 时长
        Text(
            text = formatDuration(duration),
            style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.SemiBold),
            color = color
        )
    }
}

/**
 * 详情弹窗类型
 */
enum class DetailDialogType {
    TOTAL, APP, NEW_APP, SCREENSHOT_SERVICE
}

/**
 * 详情弹窗
 */
@Composable
fun DetailDialog(
    title: String,
    records: List<SystemMonitorRecord>,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge
            )
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 400.dp) // 限制最大高度
            ) {
                if (records.isEmpty()) {
                    Text(
                        text = "暂无未运行记录",
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(16.dp)
                    )
                } else {
                    LazyColumn(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(records) { record ->
                            DetailRecordItem(record = record)
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确认")
            }
        }
    )
}

/**
 * 详情记录项
 */
@Composable
fun DetailRecordItem(
    record: SystemMonitorRecord,
    showServiceType: Boolean = false
) {
    val fullDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    val currentTime = System.currentTimeMillis()

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧时间信息
        Column(
            modifier = Modifier.weight(1f)
        ) {
            // 第一行：服务类型标识（仅在需要时显示）
            if (showServiceType) {
                Text(
                    text = when (record.monitorType) {
                        MonitorType.APP_BACKGROUND_RUNNING -> "App未运行"
                        MonitorType.SCREENSHOT_FUNCTION -> "截图服务未运行"
                        else -> "系统异常"
                    },
                    style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Bold),
                    color = when (record.monitorType) {
                        MonitorType.APP_BACKGROUND_RUNNING -> Color(0xFF2196F3) // 蓝色
                        MonitorType.SCREENSHOT_FUNCTION -> Color(0xFFFF9800) // 橙色
                        else -> MaterialTheme.colorScheme.error
                    }
                )

                Spacer(modifier = Modifier.height(2.dp))
            }

            // 关闭时间
            Text(
                text = "关闭: ${fullDateFormat.format(Date(record.startTime))}",
                style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Medium),
                color = MaterialTheme.colorScheme.error
            )

            Spacer(modifier = Modifier.height(2.dp))

            // 开启时间 - 如果没有结束时间，使用当前查询时间
            val endTime = record.endTime ?: currentTime
            Text(
                text = "开启: ${fullDateFormat.format(Date(endTime))}",
                style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Medium),
                color = Color(0xFF4CAF50) // 使用明亮的绿色
            )
        }

        // 右侧持续时长
        val displayDuration = if (record.endTime != null) {
            record.duration
        } else {
            // 对于没有结束时间的记录，计算到当前时间的持续时间
            currentTime - record.startTime
        }

        Text(
            text = formatDuration(displayDuration),
            style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.SemiBold),
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}

/**
 * 状态时间段卡片加载状态
 */
@Composable
fun StatusPeriodCardLoading(title: String) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .animateContentSize(), // 添加内容大小变化动画
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Text(
                text = "加载中...",
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 状态时间段卡片错误状态
 */
@Composable
fun StatusPeriodCardError(title: String, errorMessage: String) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .animateContentSize(), // 添加内容大小变化动画
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Text(
                text = "加载失败: $errorMessage",
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.error
            )
        }
    }
}

/**
 * 消息提示卡片
 */
@Composable
fun MessageCard(
    message: String,
    onDismiss: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (message.contains("失败")) {
                MaterialTheme.colorScheme.errorContainer
            } else if (message.contains("完成")) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.secondaryContainer
            }
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 消息图标
            Icon(
                imageVector = when {
                    message.contains("失败") -> Icons.Default.Delete
                    message.contains("完成") -> Icons.Default.Refresh
                    else -> Icons.Default.PlayArrow
                },
                contentDescription = null,
                tint = when {
                    message.contains("失败") -> MaterialTheme.colorScheme.onErrorContainer
                    message.contains("完成") -> MaterialTheme.colorScheme.onPrimaryContainer
                    else -> MaterialTheme.colorScheme.onSecondaryContainer
                },
                modifier = Modifier.size(20.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            // 消息文本
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = when {
                    message.contains("失败") -> MaterialTheme.colorScheme.onErrorContainer
                    message.contains("完成") -> MaterialTheme.colorScheme.onPrimaryContainer
                    else -> MaterialTheme.colorScheme.onSecondaryContainer
                },
                modifier = Modifier.weight(1f)
            )

            Spacer(modifier = Modifier.width(8.dp))

            // 关闭按钮
            IconButton(
                onClick = onDismiss,
                modifier = Modifier.size(24.dp)
            ) {
                Text(
                    text = "×",
                    fontSize = 18.sp,
                    color = when {
                        message.contains("失败") -> MaterialTheme.colorScheme.onErrorContainer
                        message.contains("完成") -> MaterialTheme.colorScheme.onPrimaryContainer
                        else -> MaterialTheme.colorScheme.onSecondaryContainer
                    }
                )
            }
        }
    }
}

/**
 * 悬浮消息提示卡片
 */
@Composable
fun FloatingMessageCard(
    message: String,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .animateContentSize(),
        colors = CardDefaults.cardColors(
            containerColor = when {
                message.contains("失败") -> MaterialTheme.colorScheme.errorContainer
                message.contains("完成") -> MaterialTheme.colorScheme.primaryContainer
                else -> MaterialTheme.colorScheme.secondaryContainer
            }
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 消息图标
            Icon(
                imageVector = when {
                    message.contains("失败") -> Icons.Default.Delete
                    message.contains("完成") -> Icons.Default.Refresh
                    else -> Icons.Default.PlayArrow
                },
                contentDescription = null,
                tint = when {
                    message.contains("失败") -> MaterialTheme.colorScheme.onErrorContainer
                    message.contains("完成") -> MaterialTheme.colorScheme.onPrimaryContainer
                    else -> MaterialTheme.colorScheme.onSecondaryContainer
                },
                modifier = Modifier.size(20.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            // 消息文本
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = when {
                    message.contains("失败") -> MaterialTheme.colorScheme.onErrorContainer
                    message.contains("完成") -> MaterialTheme.colorScheme.onPrimaryContainer
                    else -> MaterialTheme.colorScheme.onSecondaryContainer
                },
                modifier = Modifier.weight(1f)
            )

            Spacer(modifier = Modifier.width(8.dp))

            // 关闭按钮
            IconButton(
                onClick = onDismiss,
                modifier = Modifier.size(24.dp)
            ) {
                Text(
                    text = "×",
                    fontSize = 18.sp,
                    color = when {
                        message.contains("失败") -> MaterialTheme.colorScheme.onErrorContainer
                        message.contains("完成") -> MaterialTheme.colorScheme.onPrimaryContainer
                        else -> MaterialTheme.colorScheme.onSecondaryContainer
                    }
                )
            }
        }
    }
}

/**
 * 弹窗型通知组件
 */
@Composable
fun ToastNotification(
    message: String,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 动画状态
    var visible by remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()

    // 启动时显示动画
    LaunchedEffect(message) {
        visible = true
    }

    // 自动消失逻辑
    LaunchedEffect(message) {
        if (message.contains("完成")) {
            // 更新完成后停留1秒后消失
            kotlinx.coroutines.delay(1000)
            visible = false
            kotlinx.coroutines.delay(300) // 等待动画完成
            onDismiss()
        }
    }

    // 带动画的通知卡片
    AnimatedVisibility(
        visible = visible,
        enter = slideInVertically(
            initialOffsetY = { -it },
            animationSpec = tween(300, easing = EaseOutCubic)
        ) + fadeIn(animationSpec = tween(300)),
        exit = slideOutVertically(
            targetOffsetY = { -it },
            animationSpec = tween(300, easing = EaseInCubic)
        ) + fadeOut(animationSpec = tween(300)),
        modifier = modifier
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .animateContentSize(),
            colors = CardDefaults.cardColors(
                containerColor = when {
                    message.contains("失败") -> MaterialTheme.colorScheme.errorContainer
                    message.contains("完成") -> MaterialTheme.colorScheme.primaryContainer
                    else -> MaterialTheme.colorScheme.secondaryContainer
                }
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
            shape = RoundedCornerShape(12.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 消息图标
                Icon(
                    imageVector = when {
                        message.contains("失败") -> Icons.Default.Delete
                        message.contains("完成") -> Icons.Default.Refresh
                        else -> Icons.Default.PlayArrow
                    },
                    contentDescription = null,
                    tint = when {
                        message.contains("失败") -> MaterialTheme.colorScheme.onErrorContainer
                        message.contains("完成") -> MaterialTheme.colorScheme.onPrimaryContainer
                        else -> MaterialTheme.colorScheme.onSecondaryContainer
                    },
                    modifier = Modifier.size(20.dp)
                )

                Spacer(modifier = Modifier.width(12.dp))

                // 消息文本
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyMedium,
                    color = when {
                        message.contains("失败") -> MaterialTheme.colorScheme.onErrorContainer
                        message.contains("完成") -> MaterialTheme.colorScheme.onPrimaryContainer
                        else -> MaterialTheme.colorScheme.onSecondaryContainer
                    },
                    modifier = Modifier.weight(1f)
                )

                // 只有失败消息才显示关闭按钮
                if (message.contains("失败")) {
                    Spacer(modifier = Modifier.width(8.dp))

                    IconButton(
                        onClick = {
                            visible = false
                            // 延迟调用 onDismiss 等待动画完成
                            coroutineScope.launch {
                                kotlinx.coroutines.delay(300)
                                onDismiss()
                            }
                        },
                        modifier = Modifier.size(24.dp)
                    ) {
                        Text(
                            text = "×",
                            fontSize = 18.sp,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
            }
        }
    }
}

/**
 * 总时长详情弹窗（包含旧监控系统和新监控系统的数据）
 */
@Composable
fun TotalDetailDialog(
    onDismiss: () -> Unit,
    viewModel: StatusViewModel = hiltViewModel()
) {
    // 获取当前状态数据
    val status24Hours by viewModel.status24Hours.collectAsStateWithLifecycle()

    // 获取新监控系统的运行时记录
    var newAppRecords by remember { mutableStateOf<List<RuntimeRecord>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }

    // 加载新监控系统数据
    LaunchedEffect(Unit) {
        try {
            val records = viewModel.getNewAppRuntimeRecords()
            newAppRecords = records
        } catch (e: Exception) {
            // 处理错误
        } finally {
            isLoading = false
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "未运行总时长详情",
                style = MaterialTheme.typography.titleLarge
            )
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 400.dp)
            ) {
                if (isLoading) {
                    Text(
                        text = "加载中...",
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(16.dp)
                    )
                } else {
                    // 获取旧监控系统的记录
                    val oldSystemRecords = when (val state = status24Hours) {
                        is UiState.Success -> state.data.appDowntimeRecords + state.data.screenshotServiceDowntimeRecords
                        else -> emptyList()
                    }

                    if (oldSystemRecords.isEmpty() && newAppRecords.isEmpty()) {
                        Text(
                            text = "暂无未运行记录",
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.padding(16.dp)
                        )
                    } else {
                        // 创建统一的记录列表，按时间排序
                        data class UnifiedRecord(
                            val timestamp: Long,
                            val isOldSystem: Boolean,
                            val oldRecord: SystemMonitorRecord? = null,
                            val newRecord: RuntimeRecord? = null
                        )

                        val unifiedRecords = mutableListOf<UnifiedRecord>()

                        // 添加旧监控系统记录
                        oldSystemRecords.forEach { record ->
                            unifiedRecords.add(
                                UnifiedRecord(
                                    timestamp = record.startTime,
                                    isOldSystem = true,
                                    oldRecord = record
                                )
                            )
                        }

                        // 添加新监控系统记录
                        newAppRecords.forEach { record ->
                            unifiedRecords.add(
                                UnifiedRecord(
                                    timestamp = record.timestamp,
                                    isOldSystem = false,
                                    newRecord = record
                                )
                            )
                        }

                        // 按时间降序排序（最新的在前）
                        val sortedRecords = unifiedRecords.sortedByDescending { it.timestamp }

                        LazyColumn(
                            modifier = Modifier.fillMaxWidth(),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            items(sortedRecords) { unifiedRecord ->
                                if (unifiedRecord.isOldSystem && unifiedRecord.oldRecord != null) {
                                    DetailRecordItem(
                                        record = unifiedRecord.oldRecord,
                                        showServiceType = true
                                    )
                                } else if (!unifiedRecord.isOldSystem && unifiedRecord.newRecord != null) {
                                    NewAppDetailRecordItem(
                                        record = unifiedRecord.newRecord,
                                        showServiceType = true
                                    )
                                }
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确认")
            }
        }
    )
}

/**
 * 新监控系统详情弹窗
 */
@Composable
fun NewAppDetailDialog(
    onDismiss: () -> Unit,
    viewModel: StatusViewModel = hiltViewModel()
) {
    // 获取新监控系统的运行时记录
    var runtimeRecords by remember { mutableStateOf<List<RuntimeRecord>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }

    // 加载数据
    LaunchedEffect(Unit) {
        try {
            val records = viewModel.getNewAppRuntimeRecords()
            runtimeRecords = records
        } catch (e: Exception) {
            // 处理错误
        } finally {
            isLoading = false
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "App未运行时长详情",
                style = MaterialTheme.typography.titleLarge
            )
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 400.dp)
            ) {
                if (isLoading) {
                    Text(
                        text = "加载中...",
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(16.dp)
                    )
                } else if (runtimeRecords.isEmpty()) {
                    Text(
                        text = "暂无未运行记录",
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(16.dp)
                    )
                } else {
                    LazyColumn(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(runtimeRecords) { record ->
                            NewAppDetailRecordItem(record = record)
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确认")
            }
        }
    )
}

/**
 * 新监控系统详情记录项
 */
@Composable
fun NewAppDetailRecordItem(
    record: RuntimeRecord,
    showServiceType: Boolean = false
) {
    val fullDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 左侧时间信息
        Column(
            modifier = Modifier.weight(1f)
        ) {
            // 第一行：服务类型标识（仅在需要时显示）
            if (showServiceType) {
                Text(
                    text = "App未运行",
                    style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Bold),
                    color = Color(0xFF2196F3) // 蓝色
                )

                Spacer(modifier = Modifier.height(2.dp))
            }

            // 关闭时间（记录开始时间）
            Text(
                text = "关闭: ${fullDateFormat.format(Date(record.timestamp))}",
                style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Medium),
                color = MaterialTheme.colorScheme.error
            )

            Spacer(modifier = Modifier.height(2.dp))

            // 开启时间（使用endTime字段，如果没有则计算）
            val endTime = record.endTime ?: (record.timestamp + record.duration)
            Text(
                text = "开启: ${fullDateFormat.format(Date(endTime))}",
                style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Medium),
                color = Color(0xFF4CAF50) // 使用明亮的绿色
            )
        }

        // 右侧持续时长
        Text(
            text = formatDuration(record.duration),
            style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.SemiBold),
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}